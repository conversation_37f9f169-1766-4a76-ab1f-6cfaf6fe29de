declare interface R {
  code?: number;
  msg?: string;
  data?: object;
}

declare interface Symbole {
  del_flag?: string; // 删除标记，0未删除，1已删除
  lock_flag?: string; // 锁定标记，0已启用，1注册申请中，3注册审核拒绝，9已锁定
}

declare interface UserRoleDTO2 {
  mdmUserId?: number; // 角色被分派给一个用户，该用户的的Id代表一个用户。
  roleIds?: Array<number>; // 分派给用户的角色，该角色的Id代表一个角色
  rltSybole?: Symbole;
  startTime?: string; // 赋予角色该权限的时间
  endTime?: string; // 角色结束该权限的时间
}

declare interface FullDateTime {
  createTime?: string; // 记录创建时间
  updateTime?: string; // 记录最后被更新的时间
  startTime?: string; // 生效开始时间
  endTime?: string; // 效力结束时间
}

declare interface RListUserRoleVO {
  code?: number;
  msg?: string;
  data?: Array<UserRoleVO>;
}

declare interface UserRoleVO {
  id?: number; // 用户被分派角色，分派记录Id
  mdmUserId?: number; // 角色被分派给一个用户，该用户的的Id代表一个用户。
  userName?: string; // 角色被分派给一个用户，该用户的用户名。
  roleId?: number; // 分派给用户的角色，该角色的Id代表一个角色
  roleName?: string; // 角色被分派给一个用户，该角色的名称。
  roleCode?: string; // 角色被分派给一个用户，该角色的代码。
  rltSybole?: Symbole;
  rltTime?: FullDateTime;
}

declare interface UserEnrollmentDTO {
  userenrollmentId?: UserenrollmentId; // 用户注册处理信息主键Id。用户ID和注册ID必须对应有实际的的记录
  resolutionType?: string; // 处理类型。
  resolution?: string; // 处理结论。
  description?: string; // 处理意见。
  note?: string; // 备注。
  rltSybole?: Symbole;
}

declare interface UserenrollmentId {
  userId?: number; // 用户Id。建立关联的用户记录ID
  enrollmentId?: number; // 注册主键。建立关联的注册记录ID
}

declare interface RUserEnrollmentVO {
  code?: number;
  msg?: string;
  data?: UserEnrollmentVO;
}

declare interface SimplificationDateTime {
  createTime?: string; // 记录创建时间
  updateTime?: string; // 记录最后被更新的时间
}

declare interface UserEnrollmentVO {
  userenrollmentId?: UserenrollmentId; // 用户注册处理信息主键Id。用户ID和注册ID必须对应有实际的的记录
  userName?: string; // 进行本次处理的用户名
  resolutionType?: string; // 处理类型。
  resolution?: string; // 处理结论。
  description?: string; // 处理意见。
  note?: string; // 备注。
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface UserEnrollmentCriteria {
  resolutionType?: string; // 处理类型。
  resolution?: string; // 处理意见。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface RVOPage {
  code?: number;
  msg?: string;
  data?: VOPage;
}

declare interface VOPage {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<object>; // 数据记录
}

declare interface RListUserEnrollmentVO {
  code?: number;
  msg?: string;
  data?: Array<UserEnrollmentVO>;
}

declare interface VerifyDTO {
  userId?: number; // 注册审核人的Id。
  enrollmentId?: number; // 注册申请记录的Id。
  conclusion?: number; // 注册审核结论。‘0’代表批准同意，‘3’代表拒绝批准
  description?: string; // 审核结论的依据和解释
}

declare interface CBDDefTableVO {
  id?: number; // 数据表信息记录Id
  tableName?: string; // 医学字段数据存储的数据表名称
  tableChineseName?: string; // 医学字段数据存储的数据表中文名称
  tableRows?: number; // 数据表行数
  fieldCount?: number; // 数据表中字段数量
  avgRowLength?: number; // 数据表行平均长度
  createTime?: string; // 记录创建时间
  updateTime?: string; // 记录最后被更新的时间
  description?: string; // 数据表说明
  dataBaseId?: number; // 字段所属数据库的管理信息ID
  dataBaseName?: string; // 字段所属数据库的名称
  state?: string; // 数据表状态，包括：状态：启用，禁用,未发布，废弃。
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
}

declare interface MDMUserVO {
  id?: number; // 用户记录Id
  idCard?: string; // 用户身份证号码。
  name?: string; // 用户姓名。
  phone?: string; // 电话。
  email?: string; // 电子邮件。
  address?: string; // 地址。
  userName?: string; // 登录系统的用户名。
  avatar?: string; // 系统头像。
  nickName?: string; // 昵称。
  wxOpenid?: string; // 微信账号。
  userType?: string; // 用户类别，可以是“个人用户”或“机构用户”。
  miniOpenid?: string; // 小程序账号。
  qqOpenid?: string; // QQ账号。
  giteeLogin?: string; // 码云标识。
  salt?: string; // 盐值，用户注册时,系统用来和用户密码进行组合而生成的随机数值,称作salt值,通称为加盐值。
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
  roleVOList?: Array<RoleVO>; // 用户的角色。
  owenCBDDefTableVOSet?: Array<CBDDefTableVO>; // 用户可以访问的数据表。
}

declare interface RMDMUserVO {
  code?: number;
  msg?: string;
  data?: MDMUserVO;
}

declare interface RoleVO {
  id?: number; // 角色Id。新建角色时，该值为0
  roleName?: string; // 角色的名称。
  roleCode?: string; // 角色的代码。
  description?: string; // 角色的详细描述说明。
  note?: string; // 角色的备注信息。
  type?: string; // 角色的类别信息。分为用户角色，团队角色等。
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
}

declare interface MDMUserEnrollDTO {
  id?: number; // 用户Id。新建用户时，该值为0
  idCard?: string; // 个人的身份证号码/企业的统一社会信用代码。
  name?: string; // 个人姓名/企业名称。
  phone?: string; // 电话。
  email?: string; // 电子邮件。
  address?: string; // 地址。
  userName?: string; // 登录系统的用户名。
  password?: string; // 登录系统的口令。
  passwordAgain?: string; // 再次输入登录系统的口令。
  avatar?: string; // 系统头像。
  nickName?: string; // 昵称。
  userType?: string; // 用户类别，可以是“个人用户”或“机构用户”。
  wxOpenid?: string; // 微信账号。
  miniOpenid?: string; // 小程序账号。
  qqOpenid?: string; // QQ账号。
  giteeLogin?: string; // 码云标识。
  salt?: string; // 盐值，用户注册时,系统用来和用户密码进行组合而生成的随机数值,称作salt值,通称为加盐值。
  rltSybole?: Symbole;
  attachmentMaterialId?: Array<number>; // attachmentMaterialId是一个或多个已上传的附件证明文件的记录的主键。该键值在上传证明记录好获得。
}

declare interface PasswordDTO {
  id?: number; // 用户Id。
  password?: string; // 登录系统的新口令。必须是AES加密后的密文
  oldPassword?: string; // 登录系统的旧口令。必须是AES加密后的密文
}

declare interface MDMUserDTO {
  id?: number; // 用户Id。新建用户时，该值为0
  idCard?: string; // 个人的身份证号码/企业的统一社会信用代码。
  name?: string; // 个人姓名/企业名称。
  phone?: string; // 电话。
  email?: string; // 电子邮件。
  address?: string; // 地址。
  userName?: string; // 登录系统的用户名。
  password?: string; // 登录系统的口令。
  passwordAgain?: string; // 再次输入登录系统的口令。
  avatar?: string; // 系统头像。
  nickName?: string; // 昵称。
  userType?: string; // 用户类别，可以是“个人用户”或“机构用户”。
  wxOpenid?: string; // 微信账号。
  miniOpenid?: string; // 小程序账号。
  qqOpenid?: string; // QQ账号。
  giteeLogin?: string; // 码云标识。
  salt?: string; // 盐值，用户注册时,系统用来和用户密码进行组合而生成的随机数值,称作salt值,通称为加盐值。
  rltSybole?: Symbole;
}

declare interface MDMUserCriteria {
  id?: number; // 用户Id。新建用户时，该值为0
  idCard?: string; // 用户身份证号码。
  name?: string; // 用户姓名。
  phone?: string; // 电话。
  email?: string; // 电子邮件。
  address?: string; // 地址。
  userName?: string; // 登录系统的用户名。
  nickName?: string; // 昵称。
  wxOpenid?: string; // 微信账号。
  miniOpenid?: string; // 小程序账号。
  qqOpenid?: string; // QQ账号。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface EntityVOPage {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<object>; // 数据记录
}

declare interface REntityVOPage {
  code?: number;
  msg?: string;
  data?: EntityVOPage;
}

declare interface TeamUserRoleDTO2 {
  teamUserId?: teamUserId; // 角色被分派给一个团队成员，该Id代表一个团队成员。
  roleIds?: Array<number>; // 分派给团队成员的角色，该角色的Id代表一个角色
  rltSybole?: Symbole;
  startTime?: string; // 赋予角色该权限的时间
  endTime?: string; // 角色结束该权限的时间
}

declare interface teamUserId {
  teamId?: number; // 团队主键。建立关联的团队记录ID
  userId?: number; // 用户主键。建立关联的用户记录ID
}

declare interface RListTeamUserRoleVO {
  code?: number;
  msg?: string;
  data?: Array<TeamUserRoleVO>;
}

declare interface TeamUserId {
  teamId?: number; // 团队主键。建立关联的团队记录ID
  userId?: number; // 用户主键。建立关联的用户记录ID
}

declare interface TeamUserRoleId {
  teamUserId?: TeamUserId; // 团队用户表主键。建立关联的团队用户记录ID
  roleId?: number; // 角色主键。建立关联的角色记录ID
}

declare interface TeamUserRoleVO {
  teamUserRoleId?: TeamUserRoleId; // 团队用户角色关联表联合主键。各属性值不得为null，其必须是真实实例的ID
  teamUserId?: TeamUserId; // 团队用户表主键。建立关联的团队用户记录ID
  teamName?: string; // 团队名称
  userName?: string; // 用户名称
  name?: string; // 用户姓名
  roleId?: number; // 角色主键。建立关联的角色记录ID
  roleCode?: string; // 角色代码
  roleName?: string; // 角色名称
  rltTime?: FullDateTime;
}

declare interface RListTeamVO {
  code?: number;
  msg?: string;
  data?: Array<TeamVO>;
}

declare interface TeamVO {
  id?: number; // 团队主键。新建团队时，该值是0
  code?: string; // 团队代码
  name?: string; // 团队名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  description?: string; // 团队说明
  note?: string; // 备注说明
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
}

declare interface TeamDTO {
  id?: number; // 团队主键。新建团队时，该值是0
  code?: string; // 团队代码
  name?: string; // 团队名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  description?: string; // 团队说明
  note?: string; // 备注说明
  rltSybole?: Symbole;
}

declare interface RTeamVO {
  code?: number;
  msg?: string;
  data?: TeamVO;
}

declare interface TeamCriteria {
  id?: number; // 团队主键。新建团队时，该值是0
  code?: string; // 团队代码
  name?: string; // 团队名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface TeamUserDTO {
  teamUserId?: TeamUserId; // 团队用户关联表OrgUser的主键。其各属性值不得为null，且是有效实例ID
}

declare interface RTeamUserVO {
  code?: number;
  msg?: string;
  data?: TeamUserVO;
}

declare interface TeamUserVO {
  teamUserId?: TeamUserId; // 团队用户关联表OrgUser的主键。其各属性值不得为null，且是有效实例ID
  teamName?: string; // 团队名称
  userName?: string; // 用户名称
  rltTime?: SimplificationDateTime;
}

declare interface SourceCodeDTO {
  id?: number; // 程序源码信息主键Id。新建程序源码信息记录时，该值为0
  description?: string; // 程序源码描述。
  retrieval?: string; // 程序源码检索相关的信息。
  language?: string; // 程序源码的编程语言。
  platform?: string; // 程序执行平台。
  doi?: string; // 程序的DOI。
  fileId?: number; // 文件主键Id。程序源码文件的Id
}

declare interface CommonFileVO {
  id?: number; // 文件信息主键Id。新建文件信息记录时，该值为0
  title?: string; // 文档名字。
  parentId?: number; // 上级目录主键
  parentTitle?: string; // 目录名字。
  fullPath?: string; // 文件系统的uri地址。
  description?: string; // 文档描述。
  summary?: string; // 文档摘要。
  sizeMb?: number; // 文件大小，单位是MB
  rltTime?: SimplificationDateTime;
}

declare interface RCommonFileVO {
  code?: number;
  msg?: string;
  data?: CommonFileVO;
}

declare interface RSourceCodeVO {
  code?: number;
  msg?: string;
  data?: SourceCodeVO;
}

declare interface SourceCodeVO {
  id?: number; // 程序源码信息主键Id。新建程序源码信息记录时，该值为0
  description?: string; // 程序源码描述。
  retrieval?: string; // 程序源码检索相关的信息。
  language?: string; // 程序源码的编程语言。
  platform?: string; // 程序执行平台。
  doi?: string; // 程序的DOI。
  fileId?: number; // 文件主键Id。程序源码文件的Id
  rltTime?: SimplificationDateTime;
}

declare interface RListSourceCodeVO {
  code?: number;
  msg?: string;
  data?: Array<SourceCodeVO>;
}

declare interface RolePrivilegeDTO2 {
  roleId?: number; // 权限被赋予某个角色，该角色的的Id代表一个角色。
  privilegeIds?: Array<number>; // 赋予角色的权限，该权限的Id代表一个角色权限
  startTime?: string; // 赋予角色该权限的时间
  endTime?: string; // 角色结束该权限的时间
  rltSybole?: Symbole;
}

declare interface RListRolePrivilegeVO {
  code?: number;
  msg?: string;
  data?: Array<RolePrivilegeVO>;
}

declare interface RolePrivilegeVO {
  id?: number; // 权限被赋予某个角色，赋权记录Id
  roleId?: number; // 权限被赋予某个角色，该角色的Id代表一个角色。
  roleName?: string; // 权限被赋予某个角色，该角色的名称。
  roleCode?: string; // 权限被赋予某个角色，该角色的代码。
  privilegeId?: number; // 赋予角色的权限，该权限的Id代表一个角色权限
  privilegeName?: string; // 赋予角色的权限，该权限的名称。
  privilegeCode?: string; // 赋予角色的权限，该权限的代码。
  rltSybole?: Symbole;
  rltTime?: FullDateTime;
}

declare interface RolePrivilegeDTO {
  roleId?: number; // 权限被赋予某个角色，该角色的的Id代表一个角色。
  privilegeId?: number; // 赋予角色的权限，该权限的Id代表一个角色权限
  startTime?: string; // 赋予角色该权限的时间
  endTime?: string; // 角色结束该权限的时间
  rltSybole?: Symbole;
}

declare interface RRolePrivilegeVO {
  code?: number;
  msg?: string;
  data?: RolePrivilegeVO;
}

declare interface RoleDTO {
  id?: number; // 角色Id。新建角色时，该值为0
  roleName?: string; // 角色的名称。
  roleCode?: string; // 角色的代码。
  description?: string; // 角色的详细描述说明。
  note?: string; // 角色的备注信息。
  type?: string; // 角色的类别信息。分为用户角色，团队角色等。
  rltSybole?: Symbole;
}

declare interface RRoleVO {
  code?: number;
  msg?: string;
  data?: RoleVO;
}

declare interface RListRoleVO {
  code?: number;
  msg?: string;
  data?: Array<RoleVO>;
}

declare interface PublicationReportDTO {
  id?: number; // 文件信息主键Id。新建文件信息记录时，该值为0
  title?: string; // 文档名字。
  sizeMb?: number; // 文件大小，单位是MB
  type?: string; // 类别，取值包括：项目报告，发表刊物，会议报告
  description?: string; // 文档描述。
  summary?: string; // 文档摘要。
  fileId?: number; // 文件主键Id。报告或出版物文件的Id
}

declare interface PublicationReportVO {
  id?: number; // 文件信息主键Id。新建文件信息记录时，该值为0
  title?: string; // 文档名字。
  description?: string; // 文档描述。
  summary?: string; // 文档摘要。
  sizeMb?: number; // 文件大小，单位是MB
  type?: string; // 类别，取值包括：项目报告，发表刊物，会议报告
  fileId?: number; // 文件主键Id。报告或出版物文件的Id
  rltTime?: SimplificationDateTime;
}

declare interface RPublicationReportVO {
  code?: number;
  msg?: string;
  data?: PublicationReportVO;
}

declare interface RListPublicationReportVO {
  code?: number;
  msg?: string;
  data?: Array<PublicationReportVO>;
}

declare interface ProcessDTO {
  id?: number; // 工序主键。新建工序时，该值是0
  processName?: string; // 工序名称
  processCode?: string; // 工序代码
  description?: string; // 工序说明
  note?: string; // 备注
  type?: string; // 工序类别，分为订单处理、用户处理、机构处理、权限处理等
  rltSybole?: Symbole;
}

declare interface ProcessVO {
  id?: number; // 工序主键。新建工序时，该值是0
  processName?: string; // 工序名称
  processCode?: string; // 工序代码
  description?: string; // 工序说明
  note?: string; // 备注
  type?: string; // 工序类别，分为订单处理、用户处理、机构处理、权限处理等
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface RProcessVO {
  code?: number;
  msg?: string;
  data?: ProcessVO;
}

declare interface ProcessCriteria {
  processName?: string; // 工序名称
  processCode?: string; // 工序代码
  description?: string; // 工序说明
  type?: string; // 工序类别，分为订单处理、用户处理、机构处理、权限处理等
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface PrivilegeDTO {
  id?: number; // 权限Id。新建权限时，该值为0
  privilegeName?: string; // 该权限的名称。
  privilegeCode?: string; // 该权限的代码。
  type?: string; // 权限的类别信息。分为用户权限，团队权限等。
  description?: string; // 权限的详细描述说明。
  rltSybole?: Symbole;
}

declare interface PrivilegeVO {
  id?: number; // 权限Id。新建权限时，该值为0
  privilegeName?: string; // 该权限的名称。
  privilegeCode?: string; // 该权限的代码。
  type?: string; // 权限的类别信息。分为用户权限，团队权限等。
  description?: string; // 权限的详细描述说明。
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
}

declare interface RPrivilegeVO {
  code?: number;
  msg?: string;
  data?: PrivilegeVO;
}

declare interface RListPrivilegeVO {
  code?: number;
  msg?: string;
  data?: Array<PrivilegeVO>;
}

declare interface PaymentDTO {
  id?: number; // 支付信息主键。新建支付信息时，该值是0
  orderIds?: Array<number>; // 订单信息主键。支付关联的订单Id
  amount?: number; // 支付金额
  currency?: string; // 货币总类
  type?: string; // 支付方式类别
  accountNumber?: string; // 付款账号
  accountName?: string; // 付款账号户名
  description?: string; // 支付信息描述等
  rltSybole?: Symbole;
}

declare interface OrderVO {
  id?: number; // 订单主键。新建订单时，该值是0
  useId?: number; // 用户主键。创订单的用户主键
  userName?: string; // 用户名。创订单的用户名
  applicationId?: number; // 项目申请的主键。订单所属的项目申请的主键
  applicationTitle?: string; // 项目申请的名称。订单所属的项目申请的名称
  code?: string; // 订单编号，必须是唯一的
  state?: string; // 订单状态,订单状态，例如:“未批准”、“已批准”、“待处理”、“处理中”、“待付款”、“已付款”、“发货中”、“已发货”、“待收货”、“已收货”、“已完成、“订单取消”、“退款中”、“已退款”、“部分发货”、“退货中”、“已退货”、“关闭交易”等
  totalAmount?: number; // 订单总金额
  paymentState?: string; // 支付状态，例如"已付款"、“未付款”等
  refundState?: string; //  退款状态，例如"已退款"、“未退款”、“无退款”、“退款中”等
  invoiceState?: string; //  已开具"、“未开具”、“发票退回
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface PaymentVO {
  id?: number; // 支付信息主键。新建支付信息时，该值是0
  paymentCode?: string; // 支付记录代码
  amount?: number; // 支付金额
  currency?: string; // 货币总类
  type?: string; // 支付方式类别
  accountNumber?: string; // 付款账号
  accountName?: string; // 付款账号户名
  description?: string; // 支付信息描述等
  userId?: number; // 付款用户ID
  userName?: string; // 付款用户名
  orderVOList?: Array<OrderVO>; // 订单信息
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface RPaymentVO {
  code?: number;
  msg?: string;
  data?: PaymentVO;
}

declare interface PaymentCriteria {
  paymentCode?: string; // 支付记录代码
  currency?: string; // 货币总类
  type?: string; // 支付方式类别
  accountNumber?: string; // 付款账号
  accountName?: string; // 付款账号户名
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface RListPaymentVO {
  code?: number;
  msg?: string;
  data?: Array<PaymentVO>;
}

declare interface OrgUserId {
  orgId?: number; // 机构主键。建立关联的机构记录ID
  userId?: number; // 用户主键。建立关联的用户记录ID
}

declare interface OrgDTO {
  id?: number; // 组织机构主键。新建组织机构队时，该值是0
  usci?: string; // 社会机构代码
  name?: string; // 组织机构名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  description?: string; // 组织机构说明
  note?: string; // 备注说明
  rltSybole?: Symbole;
}

declare interface OrgVO {
  id?: number; // 组织机构主键。新建组织机构队时，该值是0
  usci?: string; // 社会机构代码
  name?: string; // 组织机构名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  description?: string; // 组织机构说明
  note?: string; // 备注说明
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
}

declare interface ROrgVO {
  code?: number;
  msg?: string;
  data?: OrgVO;
}

declare interface OrgCriteria {
  id?: number; // 组织机构主键。新建组织机构队时，该值是0
  usci?: string; // 社会机构代码
  name?: string; // 组织机构名称
  phone?: string; // 电话号码
  email?: string; // 电子邮箱
  address?: string; // 通信地址
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface OrgUserDTO {
  orgUserId?: OrgUserId; // 组织机构用户关联表OrgUser的主键。其各属性值不得为null，且是有效实例ID
}

declare interface OrgUserVO {
  orgUserId?: OrgUserId; // 组织机构用户关联表OrgUser的主键。其各属性值不得为null，且是有效实例ID
  orgName?: string; // 组织机构名称
  userName?: string; // 用户名称
  name?: string; // 用户姓名
  rltTime?: SimplificationDateTime;
}

declare interface RListOrgUserVO {
  code?: number;
  msg?: string;
  data?: Array<OrgUserVO>;
}

declare interface UpdateOrderDTO {
  id?: number; // 订单主键。新建订单时，该值是0
  totalAmount?: number; // 订单总金额
  paymentState?: string; // 支付状态，例如"已付款"、“未付款”等
  refundState?: string; //  退款状态，例如"已退款"、“未退款”、“无退款”、“退款中”等
  invoiceState?: string; //  已开具"、“未开具”、“发票退回
  rltSybole?: Symbole;
}

declare interface ROrderVO {
  code?: number;
  msg?: string;
  data?: OrderVO;
}

declare interface NewOrderDTO {
  applicationId?: number; // 项目申请的主键。订单所属的项目申请的主键
  totalAmount?: number; // 订单总金额
  rltSybole?: Symbole;
}

declare interface NewOrderItemDTO {
  fileInforId?: number; // 商品主键。建立关联的商品记录ID
  quantity?: number; // 商品明细预留数据属性
}

declare interface OrderItemVO {
  id?: number; // 订单明细主键。新建订单时，该值是0
  orderId?: number; // 订单主键。建立关联的订单记录ID
  fileInforId?: number; // 商品主键。建立关联的商品记录ID
  datasetName?: string; // 医学数据集名称。
  datasetNameCn?: string; // 医学数据集名称中文名称。
  projectCode?: string; // 项目编号。
  price?: number; // 商品价格
  quantity?: number; // 商品数量
}

declare interface RListOrderItemVO {
  code?: number;
  msg?: string;
  data?: Array<OrderItemVO>;
}

declare interface OrderCriteria {
  code?: string; // 订单编号，必须是唯一的
  state?: string; // 订单状态,订单状态，例如“新建”、“处理中”、“待付款”、“已付款”、“发货中”、“已发货”、“待收货”、“已收货”、“已完成、“订单取消”、“退款中”、“已退款”、“部分发货”、“退货中”、“已退货”、“关闭交易”等
  paymentState?: string; // 支付状态，例如"已付款"、“未付款”等
  refundState?: string; //  退款状态，例如"已退款"、“未退款”、“无退款”、“退款中”等
  invoiceState?: string; //  已开具"、“未开具”、“发票退回
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface OrderItemId {
  orderId?: number; // 订单主键。建立关联的订单记录ID
  fileInforId?: number; // 商品主键。建立关联的商品记录ID
}

declare interface OriginalDocumentVO {
  id?: number; // 原始文档信息主键Id。新建原始文档信息记录时，该值为0
  title?: string; // 文档标题。
  extensionName?: string; // 文档后缀名。
  documentType?: string; // 文档类型，可以是jpeg,word，pdf,png等。
  description?: string; // 文档说明。
  icon?: string; // 文档图标。
}

declare interface ROriginalDocumentVO {
  code?: number;
  msg?: string;
  data?: OriginalDocumentVO;
}

declare interface MedicalFieldDTO {
  id?: number; // 医学数据字段主键。新建医学字段时，该值是0
  name?: string; // 医学字段名称
  valueType?: string; // 医学字段数值类型。类型包含：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）、组合（C）
  recordCount?: number; // 医学数据字段对应的原始记录数量
  needScrubbing?: boolean; // 医学字段数据是否需要脱敏
  state?: string; // 医学字段状态，包括：状态：启用，禁用,未发布，废弃。
}

declare interface MedicalFieldVO {
  id?: number; // 医学数据字段主键。新建医学字段时，该值是0
  name?: string; // 医学字段名称
  valueType?: string; // 医学字段数值类型。类型包含：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）、组合（C）
  recordCount?: number; // 医学数据字段对应的原始记录数量
  options?: string; // 选项
  baseInfo?: string; // 基本信息
  remark?: string; // 简评
  needScrubbing?: boolean; // 医学字段数据是否需要脱敏
  description?: string; // 医学字段的说明信息
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 医学字段状态，包括：状态：启用，禁用,未发布，废弃。
  tableName?: string; // 医学字段原始数据存储的数据表英文名称。
  tableChineseName?: string; // 医学字段原始数据存储的数据表中文名称。
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
  rltTime?: FullDateTime; // 相关重要时间
}

declare interface RMedicalFieldVO {
  code?: number;
  msg?: string;
  data?: MedicalFieldVO;
}

declare interface AlternativeDTO {
  id?: number; // 直方图柱面主键。新建柱面时，该值是0
  medicalFieldId?: number; // 医学字段主键。直方图柱面数据所属的医学
  binMidpoint?: string; // 直方图柱面中心点
  frequency?: string; // 柱面中的数据数量
  percentileFlag?: string; // 柱面中的分位标志，显示5%，95%，mean均值是否在该柱面内。
}

declare interface AlternativeVO {
  id?: number; // 备选值主键。新建备选值时，该值是0
  binMidpoint?: string; // 直方图柱面中心点
  frequency?: string; // 柱面中的数据数量
  percentileFlag?: string; // 柱面中的分位标志，显示5%，95%，mean均值是否在该柱面内。
}

declare interface RAlternativeVO {
  code?: number;
  msg?: string;
  data?: AlternativeVO;
}

declare interface MedicalFieldBriefCriteria {
  searchInput?: string; // 查询条件。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface MedicalFieldCriteria {
  id?: number; // 医学数据字段主键。按主键查询。
  name?: string; // 医学字段名称
  chineseMeaning?: string; // 医学字段名称中文含义
  valueType?: string; // 医学字段数值类型。类型，包含“文本”、“整数”、“实数”、“日期时间“、”组合“、”单分类“、”多分类“
  description?: string; // 医学字段的说明信息
  tableName?: string; // 医学字段数据存储的数据表名称
  tableChineseName?: string; // 医学字段数据存储的数据表中文名称
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface StatisticTxtDTO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者或被试的数量
  recordCount?: string;
  minLength?: number; // 文本字段中，最短的文本长度
  averageLength?: number; // 文本字段中，平均文本长度
  maxLength?: number; // 文本字段中，最长的文本长度
}

declare interface StatisticLongIntDTO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者或被试的数量
  recordCount?: string;
  distinctValueCount?: number; // 不重复数值数量
  min?: number; // 最小数值
  max?: number; // 最大数值
  mean?: number; // 均值
  stdDev?: number; // 方差
  decile7?: number; // 75%分位数
  median?: number; // 中位数
  decile2?: number; // 25%分位数
}

declare interface StatisticFloatDTO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者或被试的数量
  recordCount?: string;
  distinctValueCount?: number; // 不重复数值数量
  min?: number; // 最小数值
  max?: number; // 最大数值
  mean?: number; // 均值
  stdDev?: number; // 方差
  decile7?: number; // 75%分位数
  median?: number; // 中位数
  decile2?: number; // 25%分位数
}

declare interface StatisticDateTimeVO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者或被试的数量
  recordCount?: string;
  distinctValueCount?: number; // 不重复数值数量
  min?: number; // 最小数值
  max?: number; // 最大数值
  mean?: number; // 均值
  stdDev?: number; // 方差
  decile7?: number; // 75%分位数
  median?: number; // 中位数
  decile2?: number; // 25%分位数
}

declare interface StatisticCategoricalVO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者或被试的数量
  recordCount?: string;
}

declare interface PublicationVO {
  id?: number; // 出版刊物信息主键Id。新建出版刊物信息记录时，该值为0
  title?: string; // 刊物标题。
  description?: string; // 刊物说明。
}

declare interface RPublicationVO {
  code?: number;
  msg?: string;
  data?: PublicationVO;
}

declare interface ManuscriptDTO {
  id?: number; // 手稿信息主键Id。新建手稿信息记录时，该值为0
  title?: string; // 手稿标题。
  author?: string; // 第一作者。
  magazine?: string; // 杂志。
  publicationYear?: string; // Publication year
  doi?: string; // DOI。
  fileId?: number; // 文件主键Id。手稿文件的Id
}

declare interface ManuscriptVO {
  id?: number; // 手稿信息主键Id。新建手稿信息记录时，该值为0
  title?: string; // 手稿标题。
  author?: string; // 第一作者。
  magazine?: string; // 杂志。
  publicationYear?: string; // Publication year
  doi?: string; // DOI。
  fileId?: number; // 文件主键Id。程序源码文件的Id
  rltTime?: SimplificationDateTime;
}

declare interface RManuscriptVO {
  code?: number;
  msg?: string;
  data?: ManuscriptVO;
}

declare interface RListManuscriptVO {
  code?: number;
  msg?: string;
  data?: Array<ManuscriptVO>;
}

declare interface InvoiceDTO {
  id?: number; // 发票主键。新建发票时，该值是0
  code?: string; // 发票编号，必须是唯一的
  type?: string; // 发票类别,电子发票，普通发票
  service?: string; // 发票服务项目,例如，打印服务，数据信息服务，咨询服务等
  usciTo?: string; // 发票接收方社会机构代码
  nameTo?: string; // 发票接收方名称
  usciFrom?: string; // 发票开出方社会机构代码
  nameFrom?: string; // 发票开出方名称
  taxRate?: number; // 税率
  netAmount?: number; // 税前金额
  totalAmount?: number; // 总金额
  rltSybole?: Symbole;
}

declare interface InvoiceVO {
  id?: number; // 发票主键。新建发票时，该值是0
  code?: string; // 发票编号，必须是唯一的
  type?: string; // 发票类别,电子发票，普通发票
  service?: string; // 发票服务项目,例如，打印服务，数据信息服务，咨询服务等
  usciTo?: string; // 发票接收方社会机构代码
  nameTo?: string; // 发票接收方名称
  usciFrom?: string; // 发票开出方社会机构代码
  nameFrom?: string; // 发票开出方名称
  taxRate?: number; // 税率
  netAmount?: number; // 税前金额
  totalAmount?: number; // 总金额
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface RInvoiceVO {
  code?: number;
  msg?: string;
  data?: InvoiceVO;
}

declare interface InvoiceCriteria {
  code?: string; // 发票编号，必须是唯一的
  type?: string; // 发票类别,电子发票，普通发票
  service?: string; // 发票服务项目,例如，打印服务，数据信息服务，咨询服务等
  usciTo?: string; // 发票接收方社会机构代码
  nameTo?: string; // 发票接收方名称
  usciFrom?: string; // 发票开出方社会机构代码
  nameFrom?: string; // 发票开出方名称
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface EnrollmentDTO {
  id?: number; // 注册信息主键Id。新建注册信息记录时，该值为0
  state?: string; // 注册申请的状态。
  rltSybole?: Symbole;
}

declare interface EnrollmentVO {
  id?: number; // 注册信息主键Id。新建原始文档信息记录时，该值为0
  state?: string; // 注册申请的状态。
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
  attachmentMaterialVOList?: string; // 注册申请提交的证明材料。
}

declare interface REnrollmentVO {
  code?: number;
  msg?: string;
  data?: EnrollmentVO;
}

declare interface RListEnrollmentVO {
  code?: number;
  msg?: string;
  data?: Array<EnrollmentVO>;
}

declare interface EnrollmentCriteria {
  state?: string; // 注册申请的状态。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface DatasetFileDTO {
  id?: number; // 数据文件信息主键Id。新建数据文件信息记录时，该值为0
  derivedVariableCount?: number; // 衍生变量数量。
  variableDiscription?: string; // 衍生变量描述。
  isUpdate?: boolean; // 衍生变量是否是对先前数据集的更新。
  isShared?: boolean; // 衍生变量是否供他人使用。
  retrieval?: string; // 数据文件涉及出版物的信息。
  licenseDetail?: string; // 数据文件许可信息。
  fileId?: number; // 文件主键Id。数据文件的Id
}

declare interface DatasetFileVO {
  id?: number; // 程序源码信息主键Id。新建程序源码信息记录时，该值为0
  derivedVariableCount?: number; // 衍生变量数量。
  variableDiscription?: string; // 衍生变量描述。
  isUpdate?: boolean; // 衍生变量是否是对先前数据集的更新。
  isShared?: boolean; // 衍生变量是否供他人使用。
  retrieval?: string; // 数据文件涉及出版物的信息。
  licenseDetail?: string; // 数据文件许可信息。
  fileId?: number; // 文件主键Id。数据文件的Id
  rltTime?: SimplificationDateTime;
}

declare interface RDatasetFileVO {
  code?: number;
  msg?: string;
  data?: DatasetFileVO;
}

declare interface RListDatasetFileVO {
  code?: number;
  msg?: string;
  data?: Array<DatasetFileVO>;
}

declare interface DataBaseDTO {
  address?: string;
  port?: string;
  databaseName?: string;
  userName?: string;
  password?: string;
}

declare interface CBDDefDatabaseDTO {
  id?: number; // 数据库信息记录Id。新建数据库信息时，值是0
  databaseType?: string; // 数据库的类型，可与是mysql,sqlserver,postgresql。
  driverClassName?: string; // 数据库配置驱动
  address?: string; // 数据库的URL地址，可以连接字+计算机名或IP地址
  port?: string; // 数据库的端口号
  databaseName?: string; // 数据库名称
  aliasName?: string; // 数据库别名
  userName?: string; // 访问数据库的用户名
  password?: string; // 访问数据库的口令
  characterEncoding?: string; // 数据库编码规则
  zeroDateTimeBehavior?: string; // 测试数据库连接属性文件，若连接异常，则将Java对象转为null
  useSSL?: string; // 设置安全连接属性
  useJDBCCompliantTimezoneShift?: string; // 是否使用JDBC中严格的时区转换规则兼容规则
  useLegacyDatetimeCode?: string; // 是否使用本地时区进行日期时间解析和格式化
  serverTimezone?: string; // 指定mysql服务的时区
  startTime?: string; // 生效开始时间
  endTime?: string; // 效力结束时间
  note?: string; // 数据库的备注说明信息
}

declare interface CBDDefDatabaseVO {
  id?: number; // 数据库信息记录Id
  databaseType?: string; // 数据库的类型，可与是mysql,sqlserver,postgresql。
  driverClassName?: string; // 数据库配置驱动
  address?: string; // 数据库的URL地址，可以连接字+计算机名或IP地址
  port?: string; // 数据库的端口号
  databaseName?: string; // 数据库名称
  aliasName?: string; // 数据库别名
  userName?: string; // 访问数据库的用户名
  password?: string; // 访问数据库的口令
  characterEncoding?: string; // 数据库编码规则
  zeroDateTimeBehavior?: string; // 测试数据库连接属性文件，若连接异常，则将Java对象转为null
  useSSL?: string; // 设置安全连接属性
  useJDBCCompliantTimezoneShift?: string; // 是否使用JDBC中严格的时区转换规则兼容规则
  useLegacyDatetimeCode?: string; // 是否使用本地时区进行日期时间解析和格式化
  serverTimezone?: string; // 指定mysql服务的时区
  volume?: number; // 数据库中存储的数据两多少，单位是MB
  startTime?: string; // 生效开始时间
  endTime?: string; // 效力结束时间
  note?: string;
  synchronized?: boolean;
}

declare interface RCBDDefDatabaseVO {
  code?: number;
  msg?: string;
  data?: CBDDefDatabaseVO;
}

declare interface CBDDefTableDTO {
  id?: number; // 数据表记录的主键ID。
  dataBaseId?: number; // 数据表所属数据库信息在CBDDefDataBase中的id。
  tableName?: string; // 医学字段数据存储的数据表名称
  tableChineseName?: string; // 医学字段数据存储的数据表中文名称
  tableRows?: number; // 数据表行数。
  avgRowLength?: number; // 数据表行平均长度。
  createTime?: string; // 数据表创建的时间。
  updateTime?: string; // 数据表最后更新的时间。
  description?: string; // 数据表的说明信息。
  state?: string; // 数据表状态，包括：启用，禁用,未发布，废弃。
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
}

declare interface RCBDDefTableVO {
  code?: number;
  msg?: string;
  data?: CBDDefTableVO;
}

declare interface CBDDefFieldDTO {
  id?: number; // 数据字段记录的主键ID。
  tableId?: number; // 数据表所属数据表信息在CBDDefTable中的id。
  name?: string; // 字段名称。
  unit?: string; // 字段数值计量单位。
  dataType?: string; // 字段数据类型。数据项的类型包括：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）；需录入指定数据项类型。
  description?: string; // 字段的说明信息。
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 字段状态，包括：启用，禁用,未发布，废弃。
}

declare interface CBDDefFieldVO {
  id?: number; // 数据字段信息记录Id
  name?: string; // 字段名称
  unit?: string; // 字段内数据的计量单位
  dataType?: string; // 字段数据类型，类型包含：：符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）、组合（C）
  description?: string; // 字段说明
  tableId?: number; // 字段所属数据表的管理信息的ID
  tableName?: string; // 字段所属数据表的表名
  dataBaseId?: number; // 字段所属数据库的管理信息ID
  dataBaseName?: string; // 字段所属数据库的名称
  medicalFieldId?: number; // 字段关联的医学字段在MedicalField中的id
  medicalFieldIName?: string; // 字段关联医学字段在MedicalField中的名字
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 字段状态，包括：状态：启用，禁用,未发布，废弃。
}

declare interface RCBDDefFieldVO {
  code?: number;
  msg?: string;
  data?: CBDDefFieldVO;
}

declare interface CatalogueMedicalFieldDTO {
  ctlgId?: number; // 数据分类id。
  mfId?: number; // 医学字段id。
  startDate?: string; // 医学字段加入分类的时间。
  endDate?: string; // 医学字段加入分类的过期时间。
}

declare interface RListMedicalFieldVO {
  code?: number;
  msg?: string;
  data?: Array<MedicalFieldVO>;
}

declare interface CatalogueDTO {
  id?: number; // 分类主键
  parentId?: number; // 上级分类主键
  title?: string; // 数据分类的名称
  description?: string; // 数据分类的描述说明
  state?: string; // 数据分类的状态
  type?: string; // 数据分类的类型
  notes?: string; // 数据分类的备注信息
}

declare interface CatalogueVO {
  id?: number; // 分类主键
  parentId?: number; // 上级分类主键
  title?: string; // 数据分类的名称
  description?: string; // 数据分类的描述说明
  state?: string; // 数据分类的状态
  type?: string; // 数据分类的类型
  notes?: string; // 数据分类的备注信息
  childrenCount?: number;
  dataItemCount?: number;
  hasChildOrDataItem?: boolean;
  metaCode?: string; // 基础分类对象所关联的数据集元数据代码。如果分类由数据集创建，则为数据集的英文名称；如果分类由sheet量表创建，则为sheetCode；如果由CBDDefTable表创建，则为tableName
}

declare interface RCatalogueVO {
  code?: number;
  msg?: string;
  data?: CatalogueVO;
}

declare interface AttatchmentMaterialDTO {
  id?: number; // 原始文档信息主键Id。新建原始文档信息记录时，该值为0
  title?: string; // 文档标题。
  documentType?: string; // 文档类型，可以是jpeg,word，pdf,png等。
  description?: string; // 文档说明。
  rltSybole?: Symbole;
  content?: object; // 文档内容
}

declare interface AttatchmentMaterialVO {
  id?: number; // 原始文档信息主键Id。新建原始文档信息记录时，该值为0
  title?: string; // 文档标题。
  documentType?: string; // 文档类型，可以是jpeg,word，pdf,png等。
  description?: string; // 文档说明。
  icon?: string; // 文档图标。
  contentEmpty?: boolean; // 文档内容是否为空
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
}

declare interface RAttatchmentMaterialVO {
  code?: number;
  msg?: string;
  data?: AttatchmentMaterialVO;
}

declare interface RListAttatchmentMaterialVO {
  code?: number;
  msg?: string;
  data?: Array<AttatchmentMaterialVO>;
}

declare interface RVOPageAttachmentMaterialAttatchmentMaterialVO {
  code?: number;
  msg?: string;
  data?: VOPageAttachmentMaterialAttatchmentMaterialVO;
}

declare interface VOPageAttachmentMaterialAttatchmentMaterialVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<AttatchmentMaterialVO>; // 数据记录
}

declare interface AttachmentMaterialCriteria {
  title?: string; // 文档标题。
  documentType?: string; // 文档类型，可以是jpeg,word，pdf,png等。
  description?: string; // 文档说明。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface AttachmentDTO {
  id?: number; // 附加文件信息主键Id。新建文件信息记录时，该值为0
  description?: string; // 文档描述。
  retrieval?: string; // 文件检索相关的信息。
}

declare interface AttachmentVO {
  id?: number; // 附加文件信息主键Id。新建文件信息记录时，该值为0
  description?: string; // 文档描述。
  retrieval?: string; // 文件检索相关的信息。
  fileId?: number; // 文件主键Id。附加的文件的Id
  rltTime?: SimplificationDateTime;
}

declare interface RAttachmentVO {
  code?: number;
  msg?: string;
  data?: AttachmentVO;
}

declare interface RListAttachmentVO {
  code?: number;
  msg?: string;
  data?: Array<AttachmentVO>;
}

declare interface ApplicationProcessingDTO {
  id?: number; // 项目申请处理记录主键。新建项目处理记录时，该值是0
  userId?: number; // 对项目申请进行操作处理的用户Id。用户ID必须对应有实际的的记录
  applicationId?: number; // 处理的项目申请ID。项目申请ID必须对应有实际的的记录
  resolutionType?: string; // 处理类型。
  resolution?: string; // 处理结论。包括审核通过/审核未通过两个结论
  description?: string; // 处理意见。
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface ApplicationProcessingVO {
  id?: number; // 项目申请处理记录主键。新建项目处理记录时，该值是0
  resolutionType?: string; // 处理类型。
  resolution?: string; // 处理结论。
  description?: string; // 处理意见。
  applicationId?: number; // 处理的项目申请ID。项目申请ID必须对应有实际的的记录
  userId?: number; // 对项目申请进行处理的用户Id。用户ID必须对应有实际的的记录
  rltTime?: SimplificationDateTime;
  rltSybole?: Symbole;
}

declare interface RApplicationProcessingVO {
  code?: number;
  msg?: string;
  data?: ApplicationProcessingVO;
}

declare interface RListApplicationProcessingVO {
  code?: number;
  msg?: string;
  data?: Array<ApplicationProcessingVO>;
}

declare interface ApplicationVO {
  id?: number; // 项目申请主键。新建项目申请时，该值是0
  title?: string; // 项目名称
  questionObjective?: string; // 项目研究目标
  backgroundPrinciple?: string; // 项目背景和科学原理
  methodDescription?: string; // 项目使用方法说明
  datasetTypeVolume?: string; // datasetTypeVolume
  researchValue?: string; // 项目预期的价值和成果
  keyWord1?: string; // 项目关键词1
  keyWord2?: string; // 项目关键词2
  keyWord3?: string; // 项目关键词3
  keyWord4?: string; // 项目关键词4
  summary?: string; // 项目摘要，说明项目的原理、目标、持续时间和公告卫生影响
  haveDerivant?: boolean; // 预计项目会否从现有复杂数据集中衍生出新的数据字段
  duration?: number; // 预计项目的持续时间（工期），单位”月“
  state?: string; // 覆盖后的项目状态描述，包括：草稿、待审批、审核通过、审核未通过、已完成等
  rltSybole?: Symbole;
  createTime?: string; // 记录创建时间
  updateTime?: string; // 记录最后被更新的时间
}

declare interface RApplicationVO {
  code?: number;
  msg?: string;
  data?: ApplicationVO;
}

declare interface UpdateApplicationDTO {
  id?: number; // 项目申请主键。新建项目申请时，该值是0
  title?: string; // 项目名称
  questionObjective?: string; // 项目研究目标
  backgroundPrinciple?: string; // 项目背景和科学原理
  methodDescription?: string; // 项目使用方法说明
  datasetTypeVolume?: string; // datasetTypeVolume
  researchValue?: string; // 项目预期的价值和成果
  keyWord1?: string; // 项目关键词1
  keyWord2?: string; // 项目关键词2
  keyWord3?: string; // 项目关键词3
  keyWord4?: string; // 项目关键词4
  summary?: string; // 项目摘要，说明项目的原理、目标、持续时间和公告卫生影响
  haveDerivant?: boolean; // 预计项目会否从现有复杂数据集中衍生出新的数据字段
  duration?: number; // 预计项目的持续时间（工期），单位”月“
  rltSybole?: Symbole;
}

declare interface ApplicationDTO {
  title?: string; // 项目名称
  questionObjective?: string; // 项目研究目标
  backgroundPrinciple?: string; // 项目背景和科学原理
  methodDescription?: string; // 项目使用方法说明
  datasetTypeVolume?: string; // datasetTypeVolume
  researchValue?: string; // 项目预期的价值和成果
  keyWord1?: string; // 项目关键词1
  keyWord2?: string; // 项目关键词2
  keyWord3?: string; // 项目关键词3
  keyWord4?: string; // 项目关键词4
  summary?: string; // 项目摘要，说明项目的原理、目标、持续时间和公告卫生影响
  haveDerivant?: boolean; // 预计项目会否从现有复杂数据集中衍生出新的数据字段
  duration?: number; // 预计项目的持续时间（工期），单位”月“
  rltSybole?: Symbole;
}

declare interface ApplicationCriteria {
  id?: number; // 项目申请主键。新新建项目申请时，该值是0
  title?: string; // 项目名称
  questionObjective?: string; // 项目研究目标
  keyWord1?: string; // 项目关键词1
  keyWord2?: string; // 项目关键词2
  keyWord3?: string; // 项目关键词3
  keyWord4?: string; // 项目关键词4
  summary?: string; // 项目摘要，说明项目的原理、目标、持续时间和公告卫生影响
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface DictionaryValueCriteria {
  code?: string; // 字典值编码。
  title?: string; // 字典值名称。
  value?: string; // 字典值。
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface OriginalSheetDTO {
  id?: number; // 量表信息主键Id。新建量表时，该值为0
  sheetCode?: string; // 量表英文名称
  title?: string; // 量表中文名称
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
  fileId?: number; // 量表所属医数据集的ID
}

declare interface OriginalSheetVO {
  id?: number; // 量表信息主键Id。新建量表时，该值为0
  sheetCode?: string; // 量表英文名称
  title?: string; // 量表中文名称
  state?: string; // 量表态:启用，禁用,未发布，废弃
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
  fileId?: number; // 量表所属医数据集的ID
  datasetNameCn?: string; // 数据集中文名称。
  datasetName?: string; // 数据集英文文名称。
  projectCode?: string; // 课题编码缩写。
  rltTime?: SimplificationDateTime;
}

declare interface ROriginalSheetVO {
  code?: number;
  msg?: string;
  data?: OriginalSheetVO;
}

declare interface RListOriginalSheetVO {
  code?: number;
  msg?: string;
  data?: Array<OriginalSheetVO>;
}

declare interface OriginalColumnDTO {
  id?: number; // 数据变量信息主键Id。新建数据变量时，该值为0
  name?: string; // 变量名称（英文）
  dataType?: string; // 数据类型。数据项的类型包括：字符型（S），其中不可枚举且以字符描述的形式（S1），枚举型且枚举值不超过3个（S2），代码表示的形式（S3）；布尔型（L）；数值（N）、日期（D）、日期时间（DT）、时间（T）、二进制（B）；需录入指定数据项类型。
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  state?: string; // 列状态：状态：启用，禁用,未发布，废弃
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  sheetId?: number; // 数据变量所属量表的ID
}

declare interface OriginalColumnVO {
  id?: number; // column数据列信息主键Id。新建column数据列时，该值为0
  name?: string; // 数据列名称（英文）
  dataType?: string; // 字段数据类型。数据项的类型包括：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）；需录入指定数据项类型。
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 列状态
  sheetCode?: string; // 数据列所在的工作表英文名称
  sheetId?: number; // 数据列所在的工作表名称ID
  rltTime?: SimplificationDateTime;
}

declare interface ROriginalColumnVO {
  code?: number;
  msg?: string;
  data?: OriginalColumnVO;
}

declare interface RListOriginalColumnVO {
  code?: number;
  msg?: string;
  data?: Array<OriginalColumnVO>;
}

declare interface FileInfoDTO {
  datasetNameCn?: string; // 数据集中文名称。
  datasetName?: string; // 数据集英文文名称。
  projectCode?: string; // 课题编码缩写。
  description?: string; // 数据集说明。
  diseaseType?: string; // 病种。
  createDate?: string; // 数据集创建日期。
  dataManager?: string; // 数据负责人。
  affiliatedUnit?: string; // 所属单位。
  projectName?: string; // 所属项目名称。
  projectLeader?: string; // 所属项目负责人。
  affiliatedProjectUnit?: string; // 所属项目单位。
  hasBiologicalSample?: string; // 是否有生物样本。
  biologicalSampleType?: string; // 生物样本类型。
  hasImagingData?: string; // 是否有影像数据。
  dataModalityType?: string; // 数据模态类型。
  hasEEGData?: string; // 是否有脑电数据。
  dataSignalType?: string; // 数据信号类型。
  otherDataTypes?: string; // 其他数据类型。
  uploader?: string; // 上传人。
  contactPhone?: string; // 联系电话。
  officeEmail?: string; // 办公邮箱。
}

declare interface FileInfoVO {
  id?: number; // 数据集信息主键Id。新建数据集信息记录时，该值为0
  parentId?: number; // 医学数据集原始数据存储目录主键
  parentTitle?: string; // 医学数据集原始数据存储目录名字。
  fullPath?: string; // 文件系统的uri地址。
  state?: string; // 数据集状态:启用，禁用,未发布，废弃
  datasetNameCn?: string; // 数据集中文名称。
  datasetName?: string; // 数据集英文文名称。
  projectCode?: string; // 课题编码缩写。
  description?: string; // 数据集说明。
  diseaseType?: string; // 病种。
  diseaseTypeAnnotation?: string; // 疾病类型。
  createDate?: string; // 数据集创建日期。
  dataManager?: string; // 数据负责人。
  affiliatedUnit?: string; // 所属单位。
  projectName?: string; // 所属项目名称。
  projectLeader?: string; // 所属项目负责人。
  affiliatedProjectUnit?: string; // 所属项目单位。
  hasBiologicalSample?: string; // 是否有生物样本。
  biologicalSampleType?: string; // 生物样本类型。
  hasImagingData?: boolean; // 是否有影像数据。
  dataModalityType?: string; // 数据模态类型。
  hasEEGData?: boolean; // 是否有脑电数据。
  dataSignalType?: string; // 数据信号类型。
  otherDataTypes?: string; // 其他数据类型。
  uploader?: string; // 上传人。
  contactPhone?: string; // 联系电话。
  officeEmail?: string; // 办公邮箱。
  caseCount?: string; // 数据集中病例数量。
}

declare interface RListFileInfoVO {
  code?: number;
  msg?: string;
  data?: Array<FileInfoVO>;
}

declare interface RFileInfoVO {
  code?: number;
  msg?: string;
  data?: FileInfoVO;
}

declare interface RVOPageCBDDefTableCBDDefTableVO {
  code?: number;
  msg?: string;
  data?: VOPageCBDDefTableCBDDefTableVO;
}

declare interface VOPageCBDDefTableCBDDefTableVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<CBDDefTableVO>; // 数据记录
}

declare interface RVOPageFileInforFileInfoVO {
  code?: number;
  msg?: string;
  data?: VOPageFileInforFileInfoVO;
}

declare interface VOPageFileInforFileInfoVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<FileInfoVO>; // 数据记录
}

declare interface FileInforCriteria {
  title?: string; // 文档名字。
  description?: string; // 文档描述。
  rltTime?: SimplificationDateTime;
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface AnnotationIDListDTO {
  annotationIDList?: Array<number>; // 标签ID的集合
  otherFilter?: string; // 其它字段的过滤条件
  pageNum?: number; // 记录的分页页码
  pageSize?: number; // 每页显示记录的数量
}

declare interface DirectoryDTO {
  id?: number; // 目录信息主键Id。新建目录时，该值为0
  parentId?: number; // 上级目录主键
  title?: string; // 目录名字。
  description?: string; // 目录描述。
}

declare interface DirectoryVO {
  id?: number; // 目录信息主键Id。新建目录时，该值为0
  title?: string; // 目录名字。
  parentId?: number; // 上级目录主键
  parentTitle?: string; // 目录名字。
  fullPath?: string; // 目录的完整路径。
  description?: string; // 目录描述。
  fileInfors?: Array<FileInfoVO>; // 目录内的文件。
  subdirectories?: Array<DirectoryVO>; // 目录内的子目录。
  rltTime?: SimplificationDateTime;
  empty?: boolean;
}

declare interface RDirectoryVO {
  code?: number;
  msg?: string;
  data?: DirectoryVO;
}

declare interface RListDirectoryVO {
  code?: number;
  msg?: string;
  data?: Array<DirectoryVO>;
}

declare interface DictionaryValueDTO {
  id?: number; // 字典值主键Id。新建字典值记录时，该值为0
  dictionaryId?: number; // 字典信息主键Id。当前字典值所属字典的id
  code?: string; // 字典值编码。
  title?: string; // 字典值名称。
  sortOrder?: number; // 排序值
  state?: string; // 状态：启用，禁用,未发布，废弃
  value?: string; // 字典值。
  isDefault?: boolean; // 是否是字典的默认值
}

declare interface DictionaryValueVO {
  id?: number; // 字典值主键Id。新建字典值记录时，该值为0
  dictionaryId?: number; // 字典信息主键Id。当前字典值所属字典的id
  dictionaryCode?: string; // 字典编码。
  code?: string; // 字典值编码。
  title?: string; // 字典值名称。
  sortOrder?: number; // 排序值
  state?: string; // 状态：启用，禁用,未发布，废弃
  value?: string; // 字典值。
  isDefault?: boolean; // 是否是字典的默认值
  rltTime?: SimplificationDateTime;
}

declare interface RDictionaryValueVO {
  code?: number;
  msg?: string;
  data?: DictionaryValueVO;
}

declare interface RListDictionaryValueVO {
  code?: number;
  msg?: string;
  data?: Array<DictionaryValueVO>;
}

declare interface DictionaryDTO {
  id?: number; // 字典信息主键Id。新建字典信息记录时，该值为0
  dictionaryCode?: string; // 字典编码。
  title?: string; // 字典名称。
  description?: string; // 字典描述。
  sortOrder?: number; // 排序值
  state?: string; // 状态：启用，禁用,未发布，废弃
}

declare interface DictionaryVO {
  id?: number; // 字典信息主键Id。新建字典信息记录时，该值为0
  dictionaryCode?: string; // 字典编码。
  title?: string; // 字典名称。
  description?: string; // 字典描述。
  sortOrder?: number; // 排序值
  state?: string; // 状态：启用，禁用,未发布，废弃
  rltTime?: SimplificationDateTime;
}

declare interface RDictionaryVO {
  code?: number;
  msg?: string;
  data?: DictionaryVO;
}

declare interface RListDictionaryVO {
  code?: number;
  msg?: string;
  data?: Array<DictionaryVO>;
}

declare interface CommonFileDTO {
  id?: number; // 文件信息主键Id。新建文件信息记录时，该值为0
  title?: string; // 文档名字。
  parentId?: number; // 上级目录主键
  description?: string; // 文档描述。
  summary?: string; // 文档摘要。
}

declare interface RListCommonFileVO {
  code?: number;
  msg?: string;
  data?: Array<CommonFileVO>;
}

declare interface ApplicationUserDTO {
  applicationId?: number; // 项目申请记录的Id。新建一条项目成员记录时，项目申请的Id
  userRolePairs?: Array<UserRolePair>; // 项目成员的Id与角色Id的组合。新建多条项目成员记录时，多个用户成员与角色的组合
  rltSybole?: Symbole;
  startTime?: string; // 开始成为项目成员的时间
  endTime?: string; // 终止项目成员的时间
}

declare interface UserRolePair {
  userId?: number;
  roleId?: number;
}

declare interface ApplicationUserVO {
  id?: number; // 项目申请成员记录的Id。新建一条项目成员记录时，该值为0
  applicationId?: number; // 项目申请记录的Id。新建一条项目成员记录时，项目申请的Id
  userId?: number; // 项目申请记录的Id。新建一条项目成员记录时，用户成员的Id
  applicationTitle?: string; // 项目名称
  applicationCreateTime?: string; // 项目创建时间
  updateTime?: string; // 项目最后被更新的时间
  applicationState?: string; // 覆盖后的项目状态描述，包括：草稿、待审批、审核通过、审核未通过、已完成等
  applicationDuration?: number; // 预计项目的持续时间（工期），单位”月“
  name?: string; // 成员姓名
  userName?: string; // 成员用户名
  roles?: Array<RoleVO>; // 项目中成员用承担的角色
}

declare interface RListApplicationUserVO {
  code?: number;
  msg?: string;
  data?: Array<ApplicationUserVO>;
}

declare interface MDMUserInfo {
  id?: number; // 用户Id
  idCard?: string; // 用户身份证号码。
  name?: string; // 用户姓名。
  phone?: string; // 电话。
  email?: string; // 电子邮件。
  address?: string; // 地址。
  userName?: string; // 登录系统的用户名。
  password?: string; // 登录系统的口令。
  avatar?: string; // 系统头像。
  nickName?: string; // 昵称。
  wxOpenid?: string; // 微信账号。
  miniOpenid?: string; // 小程序账号。
  qqOpenid?: string; // QQ账号。
  giteeLogin?: string; // 码云标识。
  salt?: string; // 盐值，用户注册时,系统用来和用户密码进行组合而生成的随机数值,称作salt值,通称为加盐值。
  rltSybole?: Symbole;
  rltTime?: SimplificationDateTime;
  permissions?: Array<string>; // 权限代码数组。
  roles?: Array<string>; // 角色id数组。
}

declare interface RMDMUserInfo {
  code?: number;
  msg?: string;
  data?: MDMUserInfo;
}

declare interface RVOPageEnrollmentEnrollmentVO {
  code?: number;
  msg?: string;
  data?: VOPageEnrollmentEnrollmentVO;
}

declare interface VOPageEnrollmentEnrollmentVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<EnrollmentVO>; // 数据记录
}

declare interface RListCBDDefTableVO {
  code?: number;
  msg?: string;
  data?: Array<CBDDefTableVO>;
}

declare interface ApplicationTeamId {
  applicationId?: number; // 项目申请主键。建立关联的项目记录ID
  teamId?: number; // 团队主键。建立关联的团队记录ID
}

declare interface ApplicationTeamRoleId {
  applicationTeamId?: ApplicationTeamId; // 项目团队主键。建立关联的项目团队关联表记录ID
  roleId?: number; // 角色主键。建立关联的角色记录ID
}

declare interface ApplicationTeamRoleVO {
  applicationTeamRoleId?: ApplicationTeamRoleId; // 项目团队角色关联表联合主键。各属性值不得为null，其必须是真实实例的ID
  applicationTeamId?: ApplicationTeamId; // 项目团队关联表主键。新建项目团队关联时，其属性不得为null
  applicationTitle?: string; // 项目名称
  teamName?: string; // 团队名称
  roleId?: number; // 角色主键。建立关联的角色记录ID
  roleCode?: string; // 角色代码
  roleName?: string; // 角色名称
  rltTime?: FullDateTime;
}

declare interface RListApplicationTeamRoleVO {
  code?: number;
  msg?: string;
  data?: Array<ApplicationTeamRoleVO>;
}

declare interface ApplicationTeamVO {
  applicationTeamId?: ApplicationTeamId; // 项目团队关联表主键。新建项目团队关联时，其属性不得为null
  applicationTitle?: string; // 项目名称
  teamName?: string; // 团队名称
  rltTime?: SimplificationDateTime;
}

declare interface RListApplicationTeamVO {
  code?: number;
  msg?: string;
  data?: Array<ApplicationTeamVO>;
}

declare interface RVOPageRoleRoleVO {
  code?: number;
  msg?: string;
  data?: VOPageRoleRoleVO;
}

declare interface VOPageRoleRoleVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<RoleVO>; // 数据记录
}

declare interface RListProcessVO {
  code?: number;
  msg?: string;
  data?: Array<ProcessVO>;
}

declare interface RVOPagePrivilegePrivilegeVO {
  code?: number;
  msg?: string;
  data?: VOPagePrivilegePrivilegeVO;
}

declare interface VOPagePrivilegePrivilegeVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<PrivilegeVO>; // 数据记录
}

declare interface RListOrderVO {
  code?: number;
  msg?: string;
  data?: Array<OrderVO>;
}

declare interface RListOrgVO {
  code?: number;
  msg?: string;
  data?: Array<OrgVO>;
}

declare interface RListInvoiceVO {
  code?: number;
  msg?: string;
  data?: Array<InvoiceVO>;
}

declare interface RListUserOrderProcessingVO {
  code?: number;
  msg?: string;
  data?: Array<UserOrderProcessingVO>;
}

declare interface UserOrderProcessingVO {
  id?: number; // 用户订单处理主键。新建订单处理时，该值是0
  conclusion?: string; // 处理结论
  rltTime?: SimplificationDateTime; // 相关时间
  rltSybole?: Symbole; // 相关标记
}

declare interface RLong {
  code?: number;
  msg?: string;
  data?: number;
}

declare interface HistogramVO {
  binMidpoint?: string; // 直方图柱面中心点
  frequency?: string; // 柱面中的数据数量
  percentileFlag?: string; // 柱面中的分位标志，显示5%，95%，mean均值是否在该柱面内。
}

declare interface RListHistogramVO {
  code?: number;
  msg?: string;
  data?: Array<HistogramVO>;
}

declare interface RListPublicationVO {
  code?: number;
  msg?: string;
  data?: Array<PublicationVO>;
}

declare interface PlatStatisticVO {
  medicalFieldCount?: number; // 字段数量
  recordCount?: number; // 数据记录数量
  volume?: number; // 数据容量
  dataTypeDistribution?: object; // 数据类型分布，存储各类数据的数量
  updateDistribution?: object; // 数据跟新分布，存储各个日期内，更新的数量
  caseCountInFileInfo?: object; // 各个数据集病例数量统计
  diseaseTypeCount?: number; // 疾病种类数量
  caseCount?: number; // 病例数量
  fieldCount?: number; // 字段数量数量
}

declare interface RPlatStatisticVO {
  code?: number;
  msg?: string;
  data?: PlatStatisticVO;
}

declare interface RListOriginalDocumentVO {
  code?: number;
  msg?: string;
  data?: Array<OriginalDocumentVO>;
}

declare interface MedicalFieldDataSourceVO {
  id?: number; // 医学数据字段主键。新建医学字段时，该值是0
  name?: string; // 医学字段名称
  valueType?: string; // 医学字段数值类型。类型包含：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）、组合（C）
  recordCount?: number; // 医学数据字段对应的原始记录数量
  options?: string; // 选项
  baseInfo?: string; // 基本信息
  remark?: string; // 简评
  needScrubbing?: boolean; // 医学字段数据是否需要脱敏
  description?: string; // 医学字段的说明信息
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 医学字段状态，包括：状态：启用，禁用,未发布，废弃。
  tableName?: string; // 医学字段原始数据存储的数据表英文名称。
  tableChineseName?: string; // 医学字段原始数据存储的数据表中文名称。
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
  rltTime?: FullDateTime; // 相关重要时间
  fieldVOs?: Array<CBDDefFieldVO>; // 医学数据字段包含的实际原始数据字段
}

declare interface RMedicalFieldDataSourceVO {
  code?: number;
  msg?: string;
  data?: MedicalFieldDataSourceVO;
}

declare interface RListCatalogueVO {
  code?: number;
  msg?: string;
  data?: Array<CatalogueVO>;
}

declare interface RListCBDDefFieldVO {
  code?: number;
  msg?: string;
  data?: Array<CBDDefFieldVO>;
}

declare interface RListAlternativeVO {
  code?: number;
  msg?: string;
  data?: Array<AlternativeVO>;
}

declare interface MedicalFieldDetailVO {
  id?: number; // 医学数据字段主键。新建医学字段时，该值是0
  name?: string; // 医学字段名称
  valueType?: string; // 医学字段数值类型。类型包含：字符串（S）、整数（N）、小数（F）、日期（D）、日期时间（DT）、文本（T）、枚举（E）、组合（C）
  recordCount?: number; // 医学数据字段对应的原始记录数量
  options?: string; // 选项
  baseInfo?: string; // 基本信息
  remark?: string; // 简评
  needScrubbing?: boolean; // 医学字段数据是否需要脱敏
  description?: string; // 医学字段的说明信息
  chineseMeaning?: string; // 中文含义。数据项在数据库中的中文含义，数值型变量需标注单位。最长256字符。
  explanation?: string; // 变量说明。数据项在业务领域中的概念描述。最长1024字符。
  valueExplanation?: string; // 变量值说明。枚举取值说明/值域说明。最长1024字符。
  length?: string; // 长度。数据所能录入的最大长度，其中日期（D）、日期时间（DT）为固定格式，不需填写长度。小数（F）长度需指定小数点后几位，如（3,2）定义为数值的总数是3个，并且保留两位小数比如：“9.99”。最长64字符。
  maximumValue?: string; // 最大值。当数据项类型为整数（N）、小数（F）时，可以定义最大值。最长64字符。
  minimumValue?: string; // 最小值。当数据项类型为整数（N）、小数（F）时，可以定义最小值。最长64字符。
  notes?: string; // 变量备注。数据项需特别补充说明。最长1024字符。
  state?: string; // 医学字段状态，包括：状态：启用，禁用,未发布，废弃。
  tableName?: string; // 医学字段原始数据存储的数据表英文名称。
  tableChineseName?: string; // 医学字段原始数据存储的数据表中文名称。
  visitPhase?: string; // 访视阶段-英文。最长64字符。
  visitPhaseChinese?: string; // 访视阶段-中文。最长128字符。
  rltTime?: FullDateTime; // 相关重要时间
  instanceFieldStatistics?: Array<StatisticVO>; // 医学字段统计信息。instanceSerialNumber为"total"的Instance内存放的是全体数据的统计信息。
  fullPathCatalogue?: Array<Array<object>>; // 医学字段的全部完整分类目录路径。
}

declare interface RMedicalFieldDetailVO {
  code?: number;
  msg?: string;
  data?: MedicalFieldDetailVO;
}

declare interface StatisticVO {
  id?: number; // 统计记录Id。新建统计记录时，该值为0
  participantCount?: number; // 参与者数量
  recordCount?: number; // 数据记录条数
}

declare interface RDouble {
  code?: number;
  msg?: string;
  data?: number;
}

declare interface RList {
  code?: number;
  msg?: string;
  data?: Array<Array<object>>;
}

declare interface RListCBDDefDatabaseVO {
  code?: number;
  msg?: string;
  data?: Array<CBDDefDatabaseVO>;
}

declare interface Axis {
  type?: string;
  boundaryGap?: boolean;
  min?: number;
  max?: number;
  data?: Array<string>;
}

declare interface Chart {
  title?: string;
  type?: string;
  seriesList?: Array<Series>;
  yaxis?: Axis;
  xaxis?: Axis;
}

declare interface DataBrowseChartVO {
  name?: string;
  title?: string;
  description?: string;
  statisticalChartCount?: number;
  chartList?: Array<Chart>;
}

declare interface RDataBrowseChartVO {
  code?: number;
  msg?: string;
  data?: DataBrowseChartVO;
}

declare interface Series {
  name?: string;
  type?: string;
  data?: Array<object>;
}

declare interface RByte {
  code?: number;
  msg?: string;
  data?: string;
}

declare interface RListApplicationVO {
  code?: number;
  msg?: string;
  data?: Array<ApplicationVO>;
}

declare interface DictionaryValueStatisticDTO {
  id?: number; // 字典值主键Id。新建字典值记录时，该值为0
  code?: string; // 字典值编码。
  title?: string; // 字典值名称。
  value?: string; // 字典值。
  amount?: number; // 数据集数量。
}

declare interface RListDictionaryValueStatisticDTO {
  code?: number;
  msg?: string;
  data?: Array<DictionaryValueStatisticDTO>;
}

declare interface RVOPageDictionaryValueDictionaryValueVO {
  code?: number;
  msg?: string;
  data?: VOPageDictionaryValueDictionaryValueVO;
}

declare interface VOPageDictionaryValueDictionaryValueVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<DictionaryValueVO>; // 数据记录
}

declare interface SseEmitter {
  timeout?: number;
}

declare interface RVOPageOriginalSheetOriginalSheetVO {
  code?: number;
  msg?: string;
  data?: VOPageOriginalSheetOriginalSheetVO;
}

declare interface VOPageOriginalSheetOriginalSheetVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<OriginalSheetVO>; // 数据记录
}

declare interface RVOPageOriginalColumnOriginalColumnVO {
  code?: number;
  msg?: string;
  data?: VOPageOriginalColumnOriginalColumnVO;
}

declare interface VOPageOriginalColumnOriginalColumnVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<OriginalColumnVO>; // 数据记录
}

declare interface RMapStringObject {
  code?: number;
  msg?: string;
  data?: object;
}

declare interface RBoolean {
  code?: number;
  msg?: string;
  data?: boolean;
}

declare interface FileInforMsg {
  code?: number;
  msg?: string;
  timestamp?: string;
}

declare interface RMapStringListFileInforMsg {
  code?: number;
  msg?: string;
  data?: object;
}

declare interface RListFileInforMsg {
  code?: number;
  msg?: string;
  data?: Array<FileInforMsg>;
}

declare interface RListMapStringObject {
  code?: number;
  msg?: string;
  data?: Array<object>;
}

declare interface RVOPageMedicalFieldMedicalFieldVO {
  code?: number;
  msg?: string;
  data?: VOPageMedicalFieldMedicalFieldVO;
}

declare interface VOPageMedicalFieldMedicalFieldVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<MedicalFieldVO>; // 数据记录
}

declare interface HistoryFileVO {
  id?: number; // 新医学数据集记录主键Id。
  datasetNameCn?: string; // 数据集中文名称。
  datasetName?: string; // 数据集英文文名称。
  mddList?: Array<string>;
  dataFileList?: Array<string>;
}

declare interface RHistoryFileVO {
  code?: number;
  msg?: string;
  data?: HistoryFileVO;
}

declare interface RVOPageCBDDefFieldCBDDefFieldVO {
  code?: number;
  msg?: string;
  data?: VOPageCBDDefFieldCBDDefFieldVO;
}

declare interface VOPageCBDDefFieldCBDDefFieldVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<CBDDefFieldVO>; // 数据记录
}

declare interface RVOPageDictionaryValueDictionaryVO {
  code?: number;
  msg?: string;
  data?: VOPageDictionaryValueDictionaryVO;
}

declare interface VOPageDictionaryValueDictionaryVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<DictionaryVO>; // 数据记录
}

declare interface RVOPageSystemDictionaryDictionaryVO {
  code?: number;
  msg?: string;
  data?: VOPageSystemDictionaryDictionaryVO;
}

declare interface VOPageSystemDictionaryDictionaryVO {
  totalPage?: number; // 总页数
  pageNumber?: number; // 当前页索引号
  totalElement?: number; // 数据记录的总数
  content?: Array<DictionaryVO>; // 数据记录
}

declare interface RApplicationUserVO {
  code?: number;
  msg?: string;
  data?: ApplicationUserVO;
}

declare interface PageApplicationUserVO {
  totalElements?: number;
  totalPages?: number;
  size?: number;
  content?: Array<ApplicationUserVO>;
  number?: number;
  sort?: SortObject;
  first?: boolean;
  last?: boolean;
  numberOfElements?: number;
  pageable?: PageableObject;
  empty?: boolean;
}

declare interface PageableObject {
  offset?: number;
  sort?: SortObject;
  pageSize?: number;
  unpaged?: boolean;
  paged?: boolean;
  pageNumber?: number;
}

declare interface RPageApplicationUserVO {
  code?: number;
  msg?: string;
  data?: PageApplicationUserVO;
}

declare interface SortObject {
  empty?: boolean;
  unsorted?: boolean;
  sorted?: boolean;
}
